<template>
  <div class="weather-ai-reminder">
    <!-- 标题区域 -->
    <div class="section-header">
      <div class="title-section">
        <h2 class="section-title">深解读</h2>
        <span class="section-subtitle">天气数据+个人情况</span>
      </div>
    </div>

    <!-- AI提醒内容 -->
    <div v-if="aiReminder" class="reminder-content">
      <div class="reminder-card">
        <div class="card-title">AI智能天气提醒</div>
        <div
          class="card-content markdown-body custom-markdown-body"
          v-html="renderMarkdown(aiReminder)"
        ></div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="no-data">
      <div class="no-data-text">暂无AI天气提醒</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import MarkdownIt from 'markdown-it';
import 'github-markdown-css/github-markdown-light.css';

// Props
interface IProps {
  aiReminder: string;
}

const props = defineProps<IProps>();

// 创建简化的markdown实例
const md = new MarkdownIt({
  html: false, // 不允许HTML标签
  breaks: true, // 支持换行
  linkify: true, // 自动识别链接
});

// markdown渲染函数
const renderMarkdown = (text: string): string => {
  return md.render(text);
};
</script>

<style scoped lang="scss">
.weather-ai-reminder {
  background: var(--bg-glass);
  border-radius: 20px;
  box-shadow: var(--shadow-soft);
  padding: 20px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-glass);
}

// 标题区域
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .title-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .section-title {
      font-size: 30px;
      font-weight: 600;
      color: var(--page-text-primary);
      margin: 0;
    }

    .section-subtitle {
      font-size: 24px;
      color: var(--page-text-primary);
      opacity: 0.9;
    }
  }
}

// 提醒内容
.reminder-content {
  display: flex;
  flex-direction: column;
}

// 卡片样式 - 参考WeatherRawData的weather-card样式
.reminder-card {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  border-radius: var(--border-radius-lg);
  padding: 22px 22px 22px 28px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  .card-title {
    color: var(--text-primary);
    font-size: 34px; // H5移动端适配，比原来大4px
    font-weight: 600;
    margin-bottom: 16px;
    line-height: 1.3;
  }

  .card-content {
    color: var(--person-detail-context);
    font-size: 32px; // H5移动端适配，比原来大4px
    font-weight: 450;
    line-height: 1.4;
    background: var(--primary-color-light);
    border: 2px solid var(--border-accent);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    transition: all 0.3s ease;

    // 支持HTML格式
    :deep(strong) {
      font-weight: 700;
      color: var(--text-primary);
    }

    :deep(em) {
      font-style: italic;
      color: var(--text-accent);
    }

    :deep(p) {
      margin: 0 0 12px 0;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(br) {
      line-height: 1.6;
    }

    // Markdown样式支持
    &.markdown-body.custom-markdown-body {
      background: transparent;
      word-break: break-all;
      font-size: inherit !important;

      // 重置github-markdown-css的默认字体大小
      * {
        font-size: inherit !important;
      }

      p {
        font-size: inherit !important;
        margin: 0 0 12px 0;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }

      ol li {
        list-style-type: decimal;
        font-size: inherit !important;
      }
      ul li {
        list-style-type: disc;
        font-size: inherit !important;
      }
      ul,
      ol {
        margin-bottom: 0;
        padding-left: 90px;
        font-size: inherit !important;
      }
      code {
        word-break: break-all !important;
        white-space: pre-wrap !important;
        background: var(--bg-glass);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 28px !important; // 稍微小一点但仍然清晰
      }
      pre {
        background: var(--bg-glass);
        padding: 16px;
        border-radius: 8px;
        overflow-x: auto;
        border: 1px solid var(--border-glass);
        font-size: inherit !important;
      }
      blockquote {
        border-left: 4px solid var(--accent-color);
        padding-left: 16px;
        margin: 16px 0;
        color: var(--text-secondary);
        font-style: italic;
        font-size: inherit !important;
      }
      h1, h2, h3, h4, h5, h6 {
        color: var(--text-primary);
        margin: 16px 0 8px 0;
        font-weight: 600;
        font-size: inherit !important;
      }
      h1 { font-size: 36px !important; }
      h2 { font-size: 34px !important; }
      h3 { font-size: 32px !important; }
      h4, h5, h6 { font-size: 30px !important; }

      table {
        border-collapse: collapse;
        width: 100%;
        margin: 16px 0;
        font-size: inherit !important;
      }
      th, td {
        border: 1px solid var(--border-glass);
        padding: 8px 12px;
        text-align: left;
        font-size: inherit !important;
      }
      th {
        background: var(--bg-glass);
        font-weight: 600;
      }
    }
  }
}

// 无数据状态 - 参考WeatherRawData样式
.no-data {
  text-align: center;
  padding: 40px 20px;
  background: var(--bg-glass);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  border: 1px dashed var(--border-accent);

  .no-data-text {
    font-size: 30px; // H5移动端适配，比原来大4px
    color: var(--text-primary);
    font-style: italic;
    line-height: 1.6;
  }
}

// 响应式适配 - H5移动端优化
@media (max-width: 768px) {
  .weather-ai-reminder {
    margin-bottom: 16px;
  }

  .section-header {
    margin-bottom: 20px;

    .section-title {
      font-size: 32px; // 保持相对比例
    }

    .section-subtitle {
      font-size: 20px; // 保持相对比例
    }
  }

  .reminder-card {
    padding: 18px 18px 18px 24px;

    .card-title {
      font-size: 30px;
      margin-bottom: 14px;
    }

    .card-content {
      font-size: 28px;
      padding: 16px;

      :deep(p) {
        margin: 0 0 10px 0;
      }
    }
  }

  .no-data {
    padding: 30px 16px;

    .no-data-text {
      font-size: 26px;
    }
  }
}

// 超小屏幕适配
@media (max-width: 480px) {
  .section-header {
    .section-title {
      font-size: 28px;
    }

    .section-subtitle {
      font-size: 18px;
    }
  }

  .reminder-card {
    padding: 16px 16px 16px 20px;

    .card-title {
      font-size: 26px;
    }

    .card-content {
      font-size: 24px;
      padding: 14px;

      :deep(p) {
        margin: 0 0 8px 0;
      }
    }
  }

  .no-data {
    .no-data-text {
      font-size: 22px;
    }
  }
}
</style>
