<template>
  <div class="weather-location-summaries">
    <!-- 标题区域 -->
    <div class="section-header">
      <div class="title-section">
        <h2 class="section-title">划重点</h2>
        <span class="section-subtitle">信息太多先抓重点</span>
      </div>
    </div>

    <!-- 地点选择器 -->
    <div v-if="locations.length > 1" class="location-selector">
      <div class="location-tabs">
        <button
          v-for="location in locations"
          :key="location"
          :class="['location-tab', { active: selectedLocation === location }]"
          @click="selectLocation(location)"
        >
          {{ location }}
        </button>
      </div>
    </div>

    <!-- 地点总结内容 -->
    <div v-if="currentLocationData" class="location-content">
      <!-- 地点总结卡片 -->
      <div v-if="currentLocationData.summary" class="summary-card">
        <div class="card-title">{{ currentLocationName }}天气总结</div>
        <div
          class="card-content markdown-body custom-markdown-body"
          v-html="renderMarkdown(currentLocationData.summary)"
        ></div>
      </div>

      <!-- 地点提醒卡片 -->
      <div v-if="currentLocationData.reminder" class="reminder-card">
        <div class="card-title">{{ currentLocationName }}温馨提醒</div>
        <div
          class="card-content markdown-body custom-markdown-body"
          v-html="renderMarkdown(currentLocationData.reminder)"
        ></div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="no-data">
      <div class="no-data-text">暂无地点总结数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import MarkdownIt from 'markdown-it';
import 'github-markdown-css/github-markdown-light.css';

// Props
interface IProps {
  locationSummaries: Record<string, string>;
  locationReminders: Record<string, string>;
}

const props = defineProps<IProps>();

// 创建简化的markdown实例
const md = new MarkdownIt({
  html: false, // 不允许HTML标签
  breaks: true, // 支持换行
  linkify: true, // 自动识别链接
});

// markdown渲染函数
const renderMarkdown = (text: string): string => {
  return md.render(text);
};

// 响应式数据
const selectedLocation = ref<string>('');

// 计算属性
const locations = computed(() => {
  const summaryLocations = Object.keys(props.locationSummaries || {});
  const reminderLocations = Object.keys(props.locationReminders || {});
  // 合并并去重
  const allLocations = [...summaryLocations, ...reminderLocations];
  const uniqueLocations = Array.from(new Set(allLocations));
  return uniqueLocations;
});

const currentLocationName = computed(() => {
  const [firstLocation] = locations.value;
  return selectedLocation.value || firstLocation || '';
});

const currentLocationData = computed(() => {
  const location = currentLocationName.value;
  if (!location) return null;

  return {
    summary: props.locationSummaries?.[location] || '',
    reminder: props.locationReminders?.[location] || '',
  };
});

// 方法
const selectLocation = (location: string) => {
  console.log('🌍 [WeatherLocationSummaries] 切换地点:', location);
  selectedLocation.value = location;
};



// 监听数据变化，自动选择第一个地点
watch(
  () => [props.locationSummaries, props.locationReminders],
  () => {
    if (locations.value.length > 0) {
      // 检查当前选中的地点是否在新数据中存在
      const currentLocationExists = locations.value.includes(selectedLocation.value);

      // 如果当前选中的地点不存在，或者还没有选中地点，则选择第一个
      if (!selectedLocation.value || !currentLocationExists) {
        const [firstLocation] = locations.value;
        selectedLocation.value = firstLocation;
        console.log('🎯 [WeatherLocationSummaries] 自动选择地点:', selectedLocation.value);
      }
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.weather-location-summaries {
  background: var(--bg-glass);
  border-radius: 20px;
  box-shadow: var(--shadow-soft);
  padding: 20px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-glass);
}

// 标题区域
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .title-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .section-title {
      font-size: 30px;
      font-weight: 600;
      color: var(--page-text-primary);
      margin: 0;
    }

    .section-subtitle {
      font-size: 24px;
      color: var(--page-text-primary);
      opacity: 0.9;
    }
  }
}

// 地点选择器 - 参考WeatherRawData样式
.location-selector {
  margin-bottom: 24px;

  .location-tabs {
    display: flex;
    gap: 14px;
    flex-wrap: wrap;
    justify-content: flex-start;

    .location-tab {
      padding: 14px 24px; // H5移动端适配，稍微大一点
      border: 2px solid var(--primary-color);
      background: transparent;
      border-radius: var(--border-radius-full);
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 20px; // H5移动端适配，比原来大4px
      font-weight: 600;
      color: var(--text-primary);
      min-width: 100px; // H5移动端适配，稍微大一点
      text-align: center;
      backdrop-filter: blur(10px);

      &.active {
        background: var(--primary-color);
        color: var(--on-primary-text);
        box-shadow: var(--shadow-accent);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 地点内容
.location-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

// 卡片样式 - 参考WeatherRawData的weather-card样式
.summary-card,
.reminder-card {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  border-radius: var(--border-radius-lg);
  padding: 22px 22px 22px 28px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  .card-title {
    color: var(--text-primary);
    font-size: 34px; // H5移动端适配，比原来大4px
    font-weight: 600;
    margin-bottom: 16px;
    line-height: 1.3;
  }

  .card-content {
    color: var(--person-detail-context);
    font-size: 32px; // H5移动端适配，比原来大4px
    font-weight: 450;
    line-height: 1.4;
    background: var(--primary-color-light);
    border: 2px solid var(--border-accent);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    transition: all 0.3s ease;

    // 支持HTML格式
    :deep(strong) {
      font-weight: 700;
      color: var(--text-primary);
    }

    :deep(br) {
      line-height: 1.6;
    }

    // Markdown样式支持
    &.markdown-body.custom-markdown-body {
      background: transparent;
      word-break: break-all;
      font-size: inherit !important;

      // 重置github-markdown-css的默认字体大小
      * {
        font-size: inherit !important;
      }

      p {
        font-size: inherit !important;
        margin: 0 0 12px 0;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }

      ol li {
        list-style-type: decimal;
        font-size: inherit !important;
      }
      ul li {
        list-style-type: disc;
        font-size: inherit !important;
      }
      ul,
      ol {
        margin-bottom: 0;
        padding-left: 90px;
        font-size: inherit !important;
      }
      code {
        word-break: break-all !important;
        white-space: pre-wrap !important;
        background: var(--bg-glass);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 28px !important; // 稍微小一点但仍然清晰
      }
      pre {
        background: var(--bg-glass);
        padding: 16px;
        border-radius: 8px;
        overflow-x: auto;
        border: 1px solid var(--border-glass);
        font-size: inherit !important;
      }
      blockquote {
        border-left: 4px solid var(--accent-color);
        padding-left: 16px;
        margin: 16px 0;
        color: var(--text-secondary);
        font-style: italic;
        font-size: inherit !important;
      }
      h1, h2, h3, h4, h5, h6 {
        color: var(--text-primary);
        margin: 16px 0 8px 0;
        font-weight: 600;
        font-size: inherit !important;
      }
      h1 { font-size: 36px !important; }
      h2 { font-size: 34px !important; }
      h3 { font-size: 32px !important; }
      h4, h5, h6 { font-size: 30px !important; }

      table {
        border-collapse: collapse;
        width: 100%;
        margin: 16px 0;
        font-size: inherit !important;
      }
      th, td {
        border: 1px solid var(--border-glass);
        padding: 8px 12px;
        text-align: left;
        font-size: inherit !important;
      }
      th {
        background: var(--bg-glass);
        font-weight: 600;
      }
    }
  }
}

// 无数据状态 - 参考WeatherRawData样式
.no-data {
  text-align: center;
  padding: 40px 20px;
  background: var(--bg-glass);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  border: 1px dashed var(--border-accent);

  .no-data-text {
    font-size: 30px; // H5移动端适配，比原来大4px
    color: var(--text-primary);
    font-style: italic;
    line-height: 1.6;
  }
}
</style>
